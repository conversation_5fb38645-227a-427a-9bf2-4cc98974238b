/**
 * PGM文件渲染工具模块
 * 处理PGM文件的加载、解析和渲染
 */

/**
 * PGM 特性
 * 1. PGM文件由两部分组成：文件头部分和数据部分
 * 2. 文件头包括的信息依次是：
 * 2.1. PGM文件的格式类型（P2或P5)
 * 2.2. 图像的宽度
 * 2.3. 图像的高度
 * 2.4. 图像灰度值可能的最大值
 * 这些信息在文件头中以ASCII码形式存储，并用分隔符（如空格、TAB、回车符、换行符）分开。
 */

import * as THREE from 'three'
import { markRaw } from 'vue'
import { YAMLParser } from './yamlParser.js'

/**
 * PGM渲染器类
 */
export class PGMRenderer {
  constructor(scene) {
    this.scene = scene
    this.pgmMesh = null
    this.pgmData = null // 存储PGM数据信息
    this.pgmBounds = null // 存储PGM边界信息
    this.mapConfig = null // 存储地图配置信息
  }

  /**
   * 渲染PGM文件（支持YAML配置）
   */
  async renderPGM(filePath = '/assets/screen.pgm', yamlPath = null) {
    try {
      // 移除之前的PGM网格
      this.removePreviousPGM()

      // 加载YAML配置文件
      await this.loadMapConfig(filePath, yamlPath)

      // 加载PGM文件
      const pgmData = await this.loadPGMFile(filePath)
      this.pgmData = pgmData

      // 使用YAML配置创建PGM网格
      this.pgmMesh = this.createPGMMeshWithConfig(pgmData)

      // 使用YAML配置计算PGM边界信息
      this.calculatePGMBoundsWithConfig(pgmData)

      // 添加到场景
      this.scene.add(this.pgmMesh)

      console.log('PGM文件渲染完成:', pgmData)
      console.log('地图配置:', this.mapConfig)
      console.log('PGM边界信息:', this.pgmBounds)
      return {
        success: true,
        data: pgmData,
        config: this.mapConfig,
        bounds: this.pgmBounds,
        mesh: this.pgmMesh
      }
    } catch (error) {
      console.error('PGM渲染错误:', error)
      throw error
    }
  }

  /**
   * 加载地图配置文件
   */
  async loadMapConfig(pgmFilePath, yamlPath = null) {
    try {
      // 如果没有指定YAML路径，尝试自动推断
      if (!yamlPath) {
        yamlPath = pgmFilePath.replace('.pgm', '.yaml')
      }

      console.log('尝试加载YAML配置文件:', yamlPath)

      // 加载YAML配置
      this.mapConfig = await YAMLParser.loadYAML(yamlPath)

      // 验证配置
      YAMLParser.validateMapConfig(this.mapConfig)

      console.log('YAML配置加载成功:', this.mapConfig)

    } catch (error) {
      console.warn('YAML配置文件加载失败，使用默认配置:', error.message)

      // 使用默认配置
      this.mapConfig = YAMLParser.getDefaultMapConfig()

      // 根据PGM文件路径设置image字段
      const fileName = pgmFilePath.split('/').pop()
      this.mapConfig.image = fileName
    }
  }

  /**
   * 移除之前的PGM网格
   */
  removePreviousPGM() {
    if (this.pgmMesh) {
      this.scene.remove(this.pgmMesh)
      // 清理资源
      if (this.pgmMesh.geometry) {
        this.pgmMesh.geometry.dispose()
      }
      if (this.pgmMesh.material) {
        if (this.pgmMesh.material.map) {
          this.pgmMesh.material.map.dispose()
        }
        this.pgmMesh.material.dispose()
      }
      this.pgmMesh = null
    }
  }

  /**
   * 加载PGM文件
   */
  async loadPGMFile(filePath) {
    const response = await fetch(filePath)
    if (!response.ok) {
      throw new Error(`无法加载PGM文件: ${response.status}`)
    }

    const arrayBuffer = await response.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    return this.parsePGM(uint8Array)
  }

  /**
   * 解析PGM文件
   */
  parsePGM(data) {
    let offset = 0

    // 读取头部信息
    let header = ''
    while (offset < data.length) {
      const char = String.fromCharCode(data[offset]) // 将 Unicode 码点转换为对应的字符
      header += char
      offset++

      // 检查是否读取完头部（连续两个换行符或足够的信息）
      if (header.includes('\n') && header.split('\n').length >= 4) {
        break
      }
    }
    console.log('header', header)
    // 将header字符串按行分割，并移除注释行（以#开头）和空行，返回有效内容行数组。
    const lines = header.trim().split('\n').filter(line => !line.startsWith('#'))

    if (lines[0] !== 'P5') {
      throw new Error('不支持的PGM格式，仅支持P5格式')
    }
    console.log('PGM格式:', lines)

    const [width, height] = lines[1].split(' ').map(Number)
    const maxVal = parseInt(lines[2])

    // 读取像素数据
    const pixelData = data.slice(offset)

    if (pixelData.length < width * height) {
      throw new Error('PGM文件数据不完整')
    }

    return {
      width,
      height,
      maxVal,
      data: pixelData.slice(0, width * height)
    }
  }

  /**
   * 使用配置创建PGM网格
   */
  createPGMMeshWithConfig(pgmData) {
    const { width, height, maxVal, data } = pgmData
    const config = this.mapConfig

    // 使用YAML配置中的分辨率计算实际尺寸
    const realWidth = width * config.resolution
    const realHeight = height * config.resolution

    console.log(`PGM尺寸: ${width}x${height} 像素`)
    console.log(`实际尺寸: ${realWidth.toFixed(3)}x${realHeight.toFixed(3)} 米`)
    console.log(`分辨率: ${config.resolution} 米/像素`)

    // 创建平面几何体，width - 1 和 height - 1 是为了在平面几何体上创建足够多的顶点，以便后续可以对地形进行变形。每个像素对应一个顶点，所以需要（宽度像素数-1）个分段和（高度像素数-1）个分段
    const geometry = markRaw(new THREE.PlaneGeometry(realWidth, realHeight, width - 1, height - 1))

    // 创建纹理（使用配置中的阈值）
    const texture = this.createPGMTextureWithConfig(width, height, maxVal, data, config)

    // 创建材质
    const material = markRaw(new THREE.MeshBasicMaterial({ //  创建一个网格基础材质
      map: texture, // 将之前创建的PGM纹理贴图应用到材质上
      transparent: true, //  启用透明度
      side: THREE.DoubleSide // 设置为双面渲染
    }))

    // 创建网格
    const mesh = new THREE.Mesh(geometry, material)

    // 设置位置（使用YAML配置中的原点）
    mesh.rotation.x = -Math.PI / 2  // 恢复旋转，让地图平放在地面

    // YAML中的origin是地图左下角在世界坐标系中的位置
    // 需要将地图中心移动到正确的世界坐标位置
    const originX = config.origin[0]
    const originY = config.origin[1]

    // 地图中心在世界坐标系中的位置
    const centerX = originX + realWidth / 2
    const centerZ = originY + realHeight / 2

    mesh.position.set(centerX, 0.01, centerZ)

    console.log(`地图原点: [${originX}, ${originY}]`)
    console.log(`地图中心: [${centerX}, ${centerZ}]`)

    // 添加名称标识
    mesh.name = 'pgm-map'
    mesh.userData = {
      type: 'pgm',
      width: realWidth,
      height: realHeight,
      origin: config.origin,
      resolution: config.resolution
    }

    // 使用markRaw防止Vue响应式代理
    return markRaw(mesh)
  }

  /**
   * 使用配置计算PGM边界信息
   */
  calculatePGMBoundsWithConfig(pgmData) {
    const { width, height } = pgmData
    const config = this.mapConfig

    // 使用YAML配置中的分辨率和原点
    const realWidth = width * config.resolution
    const realHeight = height * config.resolution
    const originX = config.origin[0]
    const originY = config.origin[1]

    // 计算世界坐标系中的边界
    const minX = originX
    const maxX = originX + realWidth
    const minZ = originY
    const maxZ = originY + realHeight
    const centerX = originX + realWidth / 2
    const centerZ = originY + realHeight / 2

    this.pgmBounds = {
      width: realWidth,
      height: realHeight,
      minX: minX,
      maxX: maxX,
      minZ: minZ,
      maxZ: maxZ,
      centerX: centerX,
      centerZ: centerZ,
      origin: config.origin,
      resolution: config.resolution
    }

    console.log('PGM边界（世界坐标）:', this.pgmBounds)
  }

  /**
   * 使用配置创建PGM纹理
   */
  createPGMTextureWithConfig(width, height, maxVal, data, config) {
    const canvas = document.createElement('canvas')
    canvas.width = width
    canvas.height = height
    const ctx = canvas.getContext('2d')
    const imageData = ctx.createImageData(width, height)

    // 使用YAML配置中的阈值
    const occupiedThresh = config.occupied_thresh || 0.65
    const freeThresh = config.free_thresh || 0.25
    const negate = config.negate || 0

    console.log(`使用阈值 - 占用: ${occupiedThresh}, 自由: ${freeThresh}, 反转: ${negate}`)

    for (let i = 0; i < data.length; i++) {
      const value = data[i] / maxVal // 归一化到0-1
      const pixelIndex = i * 4

      let r, g, b, a = 255

      if (negate) {
        // 反转模式
        if (value >= occupiedThresh) {
          // 自由空间 - #e8e8e8
          r = g = b = 232
        } else if (value <= freeThresh) {
          // 占用空间 - 深灰色
          r = g = b = 60
        } else {
          // 未知区域 - 中灰色
          r = g = b = 150
        }
      } else {
        // 正常模式
        if (value <= freeThresh) {
          // 自由空间 - #e8e8e8
          r = g = b = 232
        } else if (value >= occupiedThresh) {
          // 占用空间 - 深灰色
          r = g = b = 60
        } else {
          // 未知区域 - 中灰色
          r = g = b = 150
        }
      }

      imageData.data[pixelIndex] = r
      imageData.data[pixelIndex + 1] = g
      imageData.data[pixelIndex + 2] = b
      imageData.data[pixelIndex + 3] = a
    }

    ctx.putImageData(imageData, 0, 0)

    const texture = markRaw(new THREE.CanvasTexture(canvas))
    texture.flipY = false  // Three.js标准：CanvasTexture默认不需要翻转
    texture.wrapS = THREE.ClampToEdgeWrapping
    texture.wrapT = THREE.ClampToEdgeWrapping

    return texture
  }

  /**
   * 获取PGM边界信息
   */
  getPGMBounds() {
    return this.pgmBounds
  }

  /**
   * 检查是否已渲染PGM
   */
  hasPGM() {
    return this.pgmMesh !== null
  }

  /**
   * 检查点是否在PGM范围内
   */
  isPointInPGMBounds(x, z) {
    if (!this.pgmBounds) return false

    return x >= this.pgmBounds.minX &&
      x <= this.pgmBounds.maxX &&
      z >= this.pgmBounds.minZ &&
      z <= this.pgmBounds.maxZ
  }

  /**
   * 将点限制在PGM范围内
   */
  clampPointToPGMBounds(x, z) {
    if (!this.pgmBounds) return { x, z }

    return {
      x: Math.max(this.pgmBounds.minX, Math.min(this.pgmBounds.maxX, x)),
      z: Math.max(this.pgmBounds.minZ, Math.min(this.pgmBounds.maxZ, z))
    }
  }

  /**
   * 清理资源
   */
  dispose() {
    this.removePreviousPGM()
    this.pgmData = null
    this.pgmBounds = null
  }
}
